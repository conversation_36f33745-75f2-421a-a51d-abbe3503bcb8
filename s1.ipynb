import numpy as np
from scipy.special import expit as sigmoid
import matplotlib.pyplot as plt

class NeuralNetwork:
    # initializes the network
    def __init__(self, input_nodes, hidden_nodes, output_nodes, learning_rate):
        # set number of nodes in each input, hidden, output layer
        self.input_nodes = input_nodes
        self.hidden_nodes = hidden_nodes
        self.output_nodes = output_nodes
        self.learning_rate = learning_rate
        self.activation_function = lambda x: sigmoid(x)

        self.w_i_h = np.random.default_rng().normal(0, pow(self.input_nodes, -0.5), (self.hidden_nodes, self.input_nodes))
        self.w_h_o = np.random.default_rng().normal(0, pow(self.hidden_nodes, -0.5), (self.output_nodes, self.hidden_nodes))
        
    # query the network
    def query(self, input_list):
        inputs = np.array(input_list, ndmin=2).T  
        x_hidden = np.dot(self.w_i_h, inputs)  
        o_hidden = self.activation_function(x_hidden)
        x_output = np.dot(self.w_h_o, o_hidden)
        o_output = self.activation_function(x_output)
        return o_output
    
    # train the network
    def train(self, input_list, target_list):
        # =================================================================
        # بخش ۱: انتشار رو به جلو (مشابه تابع query)
        # =================================================================
        inputs = np.array(input_list, ndmin=2).T
        x_hidden = np.dot(self.w_i_h, inputs)
        o_hidden = self.activation_function(x_hidden)

        x_output = np.dot(self.w_h_o, o_hidden)
        o_output = self.activation_function(x_output)

        # =================================================================
        # بخش ۲: محاسبه خطا
        # =================================================================
        targets = np.array(target_list, ndmin=2).T
        outputs_errors = targets - o_output
        hidden_errors = np.dot(self.w_h_o.T, outputs_errors)

        # =================================================================
        # بخش ۳: به‌روزرسانی وزن‌ها (یادگیری)
        # =================================================================
        self.w_h_o += self.learning_rate * np.dot((outputs_errors * o_output * (1.0 - o_output)), o_hidden.T)
        self.w_i_h += self.learning_rate * np.dot((hidden_errors * o_hidden * (1.0 - o_hidden)), inputs.T)


# تعیین تعداد دوره یادگیری: تکرار یادگیری با تمام دیتاست را یک دوره گوییم
epochs = 5


# فراخوانی دادهای اموزش از دیتاست امنیست
train_file = open("MNIST/mnist_train.csv", "r")
train_list = train_file.readlines()
train_file.close()

# فراخوانی دادهای ازمایش از دیتاست امنیست
test_file = open("MNIST/mnist_test.csv", "r")
test_list = test_file.readlines()
test_file.close()

# تعداد نورنها در هر لایه
input_nodes = 784
hidden_nodes = 100
output_nodes = 10

# تعیین نرخ یادگیری شبکه
learning_rate = 0.2

# ساخت نمونه از شبکه عصبی با معماری بالا
nn = NeuralNetwork(input_nodes, hidden_nodes, output_nodes, learning_rate)

print("initial weights:", nn.w_i_h)

for e in range(epochs):
    for record in train_list:
        all_values = record.split(',')
        inputs = (np.asfarray(all_values[1:]) / 255.0 * 0.99) + 0.01
        targets = np.zeros(output_nodes) + 0.01
        targets[int(all_values[0])] = 0.99
        nn.train(inputs, targets)


# اموزش شبکه ساخته شده با دادهای اموزشی امنیست
for row in train_list:
    row_data = row.split(",")
    inputs = (np.asarray(row_data[1:],dtype=np.float32) / 255.0 * 0.99) + 0.01
    targets = np.zeros(output_nodes) + 0.01
    targets[int(row_data[0])] = 0.99
    nn.train(inputs, targets)

print("weights after a round of train:", nn.w_i_h)


# نمایش عکسها در دیتاست امنیست
row_data = train_list[0].split(",")
image_data = np.asarray(row_data[1:], dtype=np.float32).reshape((28, 28))
plt.imshow(image_data, cmap='Grays')

# نمایش برچسب عکس
label = row_data[0]
print("Label:", label)

# مقیاس کردن داده های ورودی به محدوده .۰۱ تا ۱ 
scaled_input = (np.asarray(row_data[1:], dtype=np.float32) / (255.0 * 0.99)) +.01


# آزمایش شبکه ساخته شده با یکی از داده های مجموعه آزمایشی 
test_row_data = test_list[0].split(",")
print("Target test is: ",test_row_data[0])

image_data = np.asarray(test_row_data[1:], dtype=np.float32).reshape((28, 28))
plt.imshow(image_data, cmap='Grays')

# نمایش برچسب عکس
label = test_row_data[0]
print("Label:", label)

nn.query((np.asarray(test_row_data[1:], dtype=np.float32) / (255.0 * 0.99)) + 0.01)



# آزمایش عملکرد شبکه به لحاظ پیش بینی همه داده های آزمایشی
score_card = []
for row in test_list:
    row_data = row.split(",")
    target_number = int(row_data[0])
    inputs = (np.asarray(row_data[1:], dtype=np.float32) / 255.0 * 0.99) + 0.01
    outputs = nn.query(inputs)
    predicted_number = np.argmax(outputs)
    # نمایش برچسب واقعی و پیش بینی شده
    # print("Actual label:", row_data[0], "Predicted label:", predicted_number)
    if target_number == predicted_number:
        score_card.append(1)
    else:
        score_card.append(0)
#گزارش عملکرد شبکه
# print("Score Card:",score_card)
score_card_array = np.asarray(score_card)   
print("\nTotal Performance:", score_card_array.sum()/score_card_array.size*100, "%")     


# بهینه یابی نرخ یادگیری
tested_learning_rate = [0.01,  0.1, 0.2 , 0.3,   0.6  ]
performance_list =     [91.24, 94.46, 95.38, 94.34, 91.06 ]
fig, ax = plt.subplots()
ax.plot(tested_learning_rate, performance_list, linestyle='--', marker='o')
ax.set_xlabel('learning rate')
ax.set_ylabel('accuracy')
ax.set_title('3-Layer NN for MNIST Handwritten Digits Classification')